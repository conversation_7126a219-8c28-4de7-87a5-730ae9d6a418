import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import statistics

class HospitalAnalytics:
    """Advanced analytics and insights for hospital data"""
    
    def __init__(self, processed_data: Dict[str, Any]):
        self.data = processed_data
        
    def calculate_growth_metrics(self) -> Dict[str, float]:
        """Calculate various growth metrics"""
        metrics = {}
        
        # Revenue growth
        revenue_data = self.data.get('revenue_summary', pd.DataFrame())
        if not revenue_data.empty:
            total_current = revenue_data['June_25'].sum()
            total_previous = revenue_data['May_25'].sum()
            
            if total_previous > 0:
                metrics['revenue_growth_rate'] = ((total_current - total_previous) / total_previous) * 100
                metrics['revenue_momentum'] = self._calculate_momentum(revenue_data, 'June_25', 'May_25')
            
            # Revenue mix analysis
            op_revenue = revenue_data[revenue_data['Category'].str.contains('OP', na=False)]['June_25'].sum()
            ip_revenue = revenue_data[revenue_data['Category'].str.contains('IP', na=False)]['June_25'].sum()
            
            if total_current > 0:
                metrics['op_revenue_percentage'] = (op_revenue / total_current) * 100
                metrics['ip_revenue_percentage'] = (ip_revenue / total_current) * 100
        
        # Performance growth
        perf_data = self.data.get('performance_report', pd.DataFrame())
        if not perf_data.empty:
            perf_growth = perf_data['Variance_Ratio'].dropna()
            if len(perf_growth) > 0:
                metrics['avg_performance_growth'] = perf_growth.mean() * 100
                metrics['performance_volatility'] = perf_growth.std() * 100
        
        return metrics
    
    def _calculate_momentum(self, data: pd.DataFrame, current_col: str, previous_col: str) -> float:
        """Calculate momentum indicator"""
        if data.empty:
            return 0
        
        growth_rates = ((data[current_col] - data[previous_col]) / data[previous_col]).dropna()
        if len(growth_rates) == 0:
            return 0
        
        positive_growth = (growth_rates > 0).sum()
        total_items = len(growth_rates)
        
        return (positive_growth / total_items) * 100
    
    def analyze_department_performance(self) -> Dict[str, Any]:
        """Analyze department-wise performance"""
        analysis = {
            'top_performers': [],
            'underperformers': [],
            'growth_leaders': [],
            'margin_leaders': []
        }
        
        # OP Department Analysis
        op_data = self.data.get('specialty_op', pd.DataFrame())
        if not op_data.empty:
            op_clean = op_data.dropna(subset=['Current_Month', 'Department'])
            
            # Top performers by volume
            top_op = op_clean.nlargest(5, 'Current_Month')
            analysis['top_performers'].extend([
                {'department': row['Department'], 'value': row['Current_Month'], 'type': 'OP Volume'}
                for _, row in top_op.iterrows()
            ])
            
            # Growth leaders
            op_growth = op_clean.dropna(subset=['Variance_Ratio'])
            if not op_growth.empty:
                growth_leaders = op_growth.nlargest(3, 'Variance_Ratio')
                analysis['growth_leaders'].extend([
                    {'department': row['Department'], 'growth': row['Variance_Ratio'] * 100, 'type': 'OP'}
                    for _, row in growth_leaders.iterrows()
                ])
        
        # IP Department Analysis
        ip_data = self.data.get('specialty_ip', pd.DataFrame())
        if not ip_data.empty:
            ip_clean = ip_data.dropna(subset=['Current_Month', 'Department'])
            
            # Top performers by volume
            top_ip = ip_clean.nlargest(5, 'Current_Month')
            analysis['top_performers'].extend([
                {'department': row['Department'], 'value': row['Current_Month'], 'type': 'IP Volume'}
                for _, row in top_ip.iterrows()
            ])
        
        # Pharmacy Margin Analysis
        pharmacy_data = self.data.get('pharmacy_margins', pd.DataFrame())
        if not pharmacy_data.empty:
            margin_leaders = pharmacy_data.nlargest(5, 'June_25')
            analysis['margin_leaders'] = [
                {'department': row['Department'], 'margin': row['June_25'] * 100}
                for _, row in margin_leaders.iterrows()
            ]
        
        return analysis
    
    def generate_financial_insights(self) -> List[str]:
        """Generate automated financial insights"""
        insights = []
        
        # Revenue insights
        revenue_data = self.data.get('revenue_summary', pd.DataFrame())
        if not revenue_data.empty:
            total_current = revenue_data['June_25'].sum()
            total_previous = revenue_data['May_25'].sum()
            
            if total_previous > 0:
                growth_rate = ((total_current - total_previous) / total_previous) * 100
                
                if growth_rate > 5:
                    insights.append(f"🚀 Strong revenue growth of {growth_rate:.1f}% observed in June 2025")
                elif growth_rate > 0:
                    insights.append(f"📈 Positive revenue growth of {growth_rate:.1f}% maintained in June 2025")
                else:
                    insights.append(f"⚠️ Revenue declined by {abs(growth_rate):.1f}% in June 2025")
            
            # Revenue mix insights
            op_revenue = revenue_data[revenue_data['Category'].str.contains('OP', na=False)]['June_25'].sum()
            ip_revenue = revenue_data[revenue_data['Category'].str.contains('IP', na=False)]['June_25'].sum()
            
            if total_current > 0:
                op_percentage = (op_revenue / total_current) * 100
                if op_percentage > 60:
                    insights.append(f"🏥 Outpatient services dominate revenue mix at {op_percentage:.1f}%")
                elif op_percentage < 40:
                    insights.append(f"🛏️ Inpatient services lead revenue generation at {100-op_percentage:.1f}%")
        
        # Performance insights
        perf_data = self.data.get('performance_report', pd.DataFrame())
        if not perf_data.empty:
            variance_data = perf_data['Variance_Ratio'].dropna()
            if len(variance_data) > 0:
                avg_variance = variance_data.mean()
                if avg_variance > 0.1:
                    insights.append("📊 Overall performance metrics show strong positive trends")
                elif avg_variance < -0.1:
                    insights.append("📉 Performance metrics indicate areas needing attention")
        
        # Pharmacy insights
        pharmacy_data = self.data.get('pharmacy_margins', pd.DataFrame())
        if not pharmacy_data.empty:
            avg_margin = pharmacy_data['June_25'].mean()
            if avg_margin > 0.25:
                insights.append(f"💊 Pharmacy margins are healthy at {avg_margin*100:.1f}% average")
            elif avg_margin < 0.15:
                insights.append(f"⚠️ Pharmacy margins need improvement at {avg_margin*100:.1f}% average")
        
        return insights
    
    def calculate_efficiency_metrics(self) -> Dict[str, float]:
        """Calculate operational efficiency metrics"""
        metrics = {}
        
        # Patient volume efficiency
        op_data = self.data.get('specialty_op', pd.DataFrame())
        ip_data = self.data.get('specialty_ip', pd.DataFrame())
        
        if not op_data.empty:
            op_total = op_data['Current_Month'].sum()
            op_departments = len(op_data.dropna(subset=['Department']))
            if op_departments > 0:
                metrics['avg_op_per_department'] = op_total / op_departments
        
        if not ip_data.empty:
            ip_total = ip_data['Current_Month'].sum()
            ip_departments = len(ip_data.dropna(subset=['Department']))
            if ip_departments > 0:
                metrics['avg_ip_per_department'] = ip_total / ip_departments
        
        # Revenue efficiency
        revenue_data = self.data.get('revenue_summary', pd.DataFrame())
        if not revenue_data.empty and not op_data.empty:
            total_revenue = revenue_data['June_25'].sum()
            total_patients = op_data['Current_Month'].sum() + ip_data['Current_Month'].sum()
            
            if total_patients > 0:
                metrics['revenue_per_patient'] = total_revenue / total_patients
        
        # Margin efficiency
        pharmacy_data = self.data.get('pharmacy_margins', pd.DataFrame())
        if not pharmacy_data.empty:
            margins = pharmacy_data['June_25'].dropna()
            if len(margins) > 0:
                metrics['margin_consistency'] = 1 - (margins.std() / margins.mean())
                metrics['top_quartile_margin'] = margins.quantile(0.75)
        
        return metrics
    
    def identify_trends(self) -> Dict[str, List[str]]:
        """Identify key trends in the data"""
        trends = {
            'positive': [],
            'negative': [],
            'neutral': []
        }
        
        # Revenue trends
        revenue_data = self.data.get('revenue_summary', pd.DataFrame())
        if not revenue_data.empty:
            growth_items = revenue_data.dropna(subset=['Variance_Ratio'])
            
            positive_growth = growth_items[growth_items['Variance_Ratio'] > 0.05]
            negative_growth = growth_items[growth_items['Variance_Ratio'] < -0.05]
            
            for _, row in positive_growth.iterrows():
                trends['positive'].append(f"{row['Subcategory']} showing {row['Variance_Ratio']*100:.1f}% growth")
            
            for _, row in negative_growth.iterrows():
                trends['negative'].append(f"{row['Subcategory']} declining by {abs(row['Variance_Ratio'])*100:.1f}%")
        
        # Department trends
        op_data = self.data.get('specialty_op', pd.DataFrame())
        if not op_data.empty:
            op_trends = op_data.dropna(subset=['Variance_Ratio'])
            
            growing_depts = op_trends[op_trends['Variance_Ratio'] > 0.1]
            declining_depts = op_trends[op_trends['Variance_Ratio'] < -0.1]
            
            for _, row in growing_depts.head(3).iterrows():
                trends['positive'].append(f"OP {row['Department']} growing by {row['Variance_Ratio']*100:.1f}%")
            
            for _, row in declining_depts.head(3).iterrows():
                trends['negative'].append(f"OP {row['Department']} declining by {abs(row['Variance_Ratio'])*100:.1f}%")
        
        # Pharmacy trends
        pharmacy_data = self.data.get('pharmacy_margins', pd.DataFrame())
        if not pharmacy_data.empty:
            # Compare June vs May margins
            improving_margins = pharmacy_data[pharmacy_data['June_25'] > pharmacy_data['May_25']]
            declining_margins = pharmacy_data[pharmacy_data['June_25'] < pharmacy_data['May_25']]
            
            if len(improving_margins) > len(declining_margins):
                trends['positive'].append("Pharmacy margins generally improving across departments")
            elif len(declining_margins) > len(improving_margins):
                trends['negative'].append("Pharmacy margins showing decline in multiple departments")
            else:
                trends['neutral'].append("Pharmacy margins remain stable across departments")
        
        return trends
    
    def generate_recommendations(self) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Analyze growth patterns
        growth_metrics = self.calculate_growth_metrics()
        
        if growth_metrics.get('revenue_growth_rate', 0) < 0:
            recommendations.append("🎯 Focus on revenue recovery strategies and cost optimization")
        
        if growth_metrics.get('op_revenue_percentage', 0) > 70:
            recommendations.append("🏥 Consider expanding inpatient services to balance revenue mix")
        
        # Department performance recommendations
        dept_analysis = self.analyze_department_performance()
        
        if len(dept_analysis['growth_leaders']) > 0:
            top_grower = dept_analysis['growth_leaders'][0]
            recommendations.append(f"📈 Replicate success strategies from {top_grower['department']} across other departments")
        
        # Efficiency recommendations
        efficiency = self.calculate_efficiency_metrics()
        
        if efficiency.get('margin_consistency', 0) < 0.7:
            recommendations.append("💊 Standardize pharmacy pricing and procurement strategies")
        
        if efficiency.get('revenue_per_patient', 0) < 10000:
            recommendations.append("💰 Explore opportunities to increase average revenue per patient")
        
        # Trend-based recommendations
        trends = self.identify_trends()
        
        if len(trends['negative']) > len(trends['positive']):
            recommendations.append("⚠️ Implement immediate action plans for declining areas")
        
        if len(trends['positive']) > 3:
            recommendations.append("🚀 Scale successful initiatives to maximize growth potential")
        
        return recommendations
    
    def get_executive_summary(self) -> Dict[str, Any]:
        """Generate executive summary with key insights"""
        summary = {
            'key_metrics': self.calculate_growth_metrics(),
            'insights': self.generate_financial_insights(),
            'trends': self.identify_trends(),
            'recommendations': self.generate_recommendations(),
            'department_analysis': self.analyze_department_performance(),
            'efficiency_metrics': self.calculate_efficiency_metrics()
        }
        
        return summary
