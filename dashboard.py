import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from data_processor import HospitalDataProcessor
from analytics import HospitalAnalytics
from visualizations import *

# Configure page
st.set_page_config(
    page_title="Hospital Analytics Dashboard",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for dark theme
st.markdown("""
<style>
    .main {
        background-color: #0e1117;
    }
    .stApp {
        background-color: #0e1117;
    }
    .metric-card {
        background-color: #1e2130;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #2e3440;
        margin: 0.5rem 0;
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #4ade80;
    }
    .metric-label {
        font-size: 0.9rem;
        color: #9ca3af;
        margin-bottom: 0.5rem;
    }
    .insight-card {
        background-color: #1e2130;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #3b82f6;
        margin: 0.5rem 0;
    }
    .sidebar .sidebar-content {
        background-color: #1e2130;
    }
    h1, h2, h3 {
        color: #f8fafc;
    }
    .stSelectbox label, .stMultiselect label {
        color: #f8fafc;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_data():
    """Load and process hospital data"""
    processor = HospitalDataProcessor("3.Monthly Dash Board - June '25.xlsx")
    processed_data = processor.process_all_data()
    key_metrics = processor.get_key_metrics()

    # Initialize analytics
    analytics = HospitalAnalytics(processed_data)
    executive_summary = analytics.get_executive_summary()

    return processed_data, key_metrics, executive_summary

def create_metric_card(label, value, delta=None, delta_color="normal"):
    """Create a custom metric card"""
    delta_html = ""
    if delta is not None:
        color = "#4ade80" if delta_color == "normal" else "#ef4444" if delta_color == "inverse" else "#6b7280"
        delta_html = f'<div style="color: {color}; font-size: 0.8rem;">{"+" if delta > 0 else ""}{delta:.1f}%</div>'
    
    return f"""
    <div class="metric-card">
        <div class="metric-label">{label}</div>
        <div class="metric-value">{value:,.0f}</div>
        {delta_html}
    </div>
    """

def create_revenue_chart(data):
    """Create revenue analysis chart"""
    if data.empty:
        return go.Figure()
    
    # Extract revenue data
    revenue_data = data[data['Category'].str.contains('Revenue', na=False)]
    
    if revenue_data.empty:
        return go.Figure()
    
    fig = go.Figure()
    
    # Add bars for current and previous month
    fig.add_trace(go.Bar(
        name='May 2025',
        x=revenue_data['Subcategory'],
        y=revenue_data['May_25'],
        marker_color='#3b82f6',
        text=revenue_data['May_25'],
        texttemplate='%{text:,.0f}',
        textposition='outside'
    ))
    
    fig.add_trace(go.Bar(
        name='June 2025',
        x=revenue_data['Subcategory'],
        y=revenue_data['June_25'],
        marker_color='#10b981',
        text=revenue_data['June_25'],
        texttemplate='%{text:,.0f}',
        textposition='outside'
    ))
    
    fig.update_layout(
        title='Revenue Comparison: May vs June 2025',
        xaxis_title='Revenue Categories',
        yaxis_title='Amount (₹)',
        barmode='group',
        template='plotly_dark',
        height=500,
        showlegend=True
    )
    
    return fig

def create_performance_trends(data):
    """Create performance trends chart"""
    if data.empty:
        return go.Figure()
    
    # Filter numeric data
    numeric_data = data.dropna(subset=['Previous_Month', 'Current_Month'])
    
    if numeric_data.empty:
        return go.Figure()
    
    fig = go.Figure()
    
    # Add trend lines
    categories = numeric_data['Metric'].head(10)  # Top 10 metrics
    
    for i, category in enumerate(categories):
        row_data = numeric_data[numeric_data['Metric'] == category].iloc[0]
        
        fig.add_trace(go.Scatter(
            x=['Previous Month', 'Current Month'],
            y=[row_data['Previous_Month'], row_data['Current_Month']],
            mode='lines+markers',
            name=category,
            line=dict(width=3),
            marker=dict(size=8)
        ))
    
    fig.update_layout(
        title='Performance Trends: Key Metrics',
        xaxis_title='Period',
        yaxis_title='Value',
        template='plotly_dark',
        height=500,
        showlegend=True
    )
    
    return fig

def create_pharmacy_margins_chart(data):
    """Create pharmacy margins analysis"""
    if data.empty:
        return go.Figure()
    
    # Get top departments by June margin
    top_depts = data.nlargest(15, 'June_25')
    
    fig = go.Figure()
    
    # Add traces for each month
    months = ['Apr_25', 'May_25', 'June_25']
    colors = ['#ef4444', '#f59e0b', '#10b981']
    month_names = ['April 2025', 'May 2025', 'June 2025']
    
    for month, color, name in zip(months, colors, month_names):
        fig.add_trace(go.Bar(
            name=name,
            x=top_depts['Department'],
            y=top_depts[month] * 100,  # Convert to percentage
            marker_color=color,
            text=top_depts[month] * 100,
            texttemplate='%{text:.1f}%',
            textposition='outside'
        ))
    
    fig.update_layout(
        title='Pharmacy Gross Margin by Department',
        xaxis_title='Department',
        yaxis_title='Gross Margin (%)',
        barmode='group',
        template='plotly_dark',
        height=600,
        showlegend=True,
        xaxis_tickangle=-45
    )
    
    return fig

def create_specialty_analysis(op_data, ip_data):
    """Create specialty-wise analysis"""
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=('Outpatient Trends', 'Inpatient Trends'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # OP Analysis
    if not op_data.empty:
        op_clean = op_data.dropna(subset=['Current_Month']).head(10)
        fig.add_trace(
            go.Bar(
                x=op_clean['Department'],
                y=op_clean['Current_Month'],
                name='OP Current',
                marker_color='#3b82f6',
                text=op_clean['Current_Month'],
                texttemplate='%{text:,.0f}',
                textposition='outside'
            ),
            row=1, col=1
        )
    
    # IP Analysis
    if not ip_data.empty:
        ip_clean = ip_data.dropna(subset=['Current_Month']).head(10)
        fig.add_trace(
            go.Bar(
                x=ip_clean['Department'],
                y=ip_clean['Current_Month'],
                name='IP Current',
                marker_color='#10b981',
                text=ip_clean['Current_Month'],
                texttemplate='%{text:,.0f}',
                textposition='outside'
            ),
            row=1, col=2
        )
    
    fig.update_layout(
        title='Specialty-wise Patient Volume Analysis',
        template='plotly_dark',
        height=500,
        showlegend=True
    )
    
    fig.update_xaxes(tickangle=-45)
    
    return fig

def main():
    # Header
    st.title("🏥 Hospital Analytics Dashboard")
    st.markdown("### Believers Church Medical College Hospital - June 2025")
    
    # Load data
    try:
        data, metrics, executive_summary = load_data()
        
        # Sidebar
        st.sidebar.title("📊 Navigation")
        page = st.sidebar.selectbox(
            "Select Analysis",
            ["Overview", "Revenue Analysis", "Performance Metrics", "Pharmacy Analysis", "Specialty Analysis", "Advanced Analytics", "Executive Summary", "Insights"]
        )
        
        if page == "Overview":
            st.header("📈 Key Performance Indicators")
            
            # KPI Cards
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.markdown(
                    create_metric_card("Total Revenue", metrics.get('total_revenue', 0), 
                                     metrics.get('revenue_growth', 0) * 100),
                    unsafe_allow_html=True
                )
            
            with col2:
                st.markdown(
                    create_metric_card("OP Revenue", metrics.get('op_revenue', 0)),
                    unsafe_allow_html=True
                )
            
            with col3:
                st.markdown(
                    create_metric_card("IP Revenue", metrics.get('ip_revenue', 0)),
                    unsafe_allow_html=True
                )
            
            with col4:
                st.markdown(
                    create_metric_card("Total Patients", 
                                     metrics.get('op_patients', 0) + metrics.get('ip_patients', 0)),
                    unsafe_allow_html=True
                )
            
            # Quick insights
            st.header("🔍 Quick Insights")
            
            insights = [
                "Revenue shows positive growth trend compared to previous month",
                "Outpatient services continue to be the primary revenue driver",
                "Pharmacy margins are performing well across most departments",
                "Patient volume remains stable with seasonal variations"
            ]
            
            for insight in insights:
                st.markdown(f"""
                <div class="insight-card">
                    💡 {insight}
                </div>
                """, unsafe_allow_html=True)
        
        elif page == "Revenue Analysis":
            st.header("💰 Revenue Analysis")
            fig = create_revenue_chart(data['revenue_summary'])
            st.plotly_chart(fig, use_container_width=True)
            
            # Revenue breakdown table
            if not data['revenue_summary'].empty:
                st.subheader("Revenue Breakdown")
                st.dataframe(
                    data['revenue_summary'][['Category', 'Subcategory', 'June_25', 'Variance_Ratio']].head(10),
                    use_container_width=True
                )
        
        elif page == "Performance Metrics":
            st.header("📊 Performance Metrics")
            fig = create_performance_trends(data['performance_report'])
            st.plotly_chart(fig, use_container_width=True)
            
            # Performance table
            if not data['performance_report'].empty:
                st.subheader("Key Performance Indicators")
                st.dataframe(
                    data['performance_report'][['Metric', 'Current_Month', 'Variance_Ratio']].head(10),
                    use_container_width=True
                )
        
        elif page == "Pharmacy Analysis":
            st.header("💊 Pharmacy Gross Margin Analysis")
            fig = create_pharmacy_margins_chart(data['pharmacy_margins'])
            st.plotly_chart(fig, use_container_width=True)
            
            # Top performing departments
            if not data['pharmacy_margins'].empty:
                st.subheader("Top Performing Departments")
                top_margins = data['pharmacy_margins'].nlargest(10, 'June_25')
                st.dataframe(top_margins[['Department', 'June_25']], use_container_width=True)
        
        elif page == "Specialty Analysis":
            st.header("🏥 Specialty-wise Analysis")

            # Comparative analysis
            fig = create_comparative_analysis(data['specialty_op'], data['specialty_ip'])
            st.plotly_chart(fig, use_container_width=True)

            # Specialty tables
            col1, col2 = st.columns(2)

            with col1:
                st.subheader("Top OP Departments")
                if not data['specialty_op'].empty:
                    st.dataframe(
                        data['specialty_op'][['Department', 'Current_Month']].head(10),
                        use_container_width=True
                    )

            with col2:
                st.subheader("Top IP Departments")
                if not data['specialty_ip'].empty:
                    st.dataframe(
                        data['specialty_ip'][['Department', 'Current_Month']].head(10),
                        use_container_width=True
                    )

        elif page == "Advanced Analytics":
            st.header("🔬 Advanced Analytics")

            # Trend analysis
            st.subheader("📈 Comprehensive Trend Analysis")
            trend_fig = create_trend_analysis(data['performance_report'])
            st.plotly_chart(trend_fig, use_container_width=True)

            # Financial sunburst
            st.subheader("💰 Financial Structure Analysis")
            sunburst_fig = create_financial_sunburst(data['revenue_summary'], data['financial_summary'])
            st.plotly_chart(sunburst_fig, use_container_width=True)

            # Performance radar
            col1, col2 = st.columns(2)

            with col1:
                st.subheader("🎯 Performance Radar")
                radar_fig = create_performance_radar(data['performance_report'])
                st.plotly_chart(radar_fig, use_container_width=True)

            with col2:
                st.subheader("🌡️ Department Heatmap")
                heatmap_fig = create_department_heatmap(data['pharmacy_margins'])
                st.plotly_chart(heatmap_fig, use_container_width=True)

        elif page == "Executive Summary":
            st.header("📋 Executive Summary")

            # Key metrics from analytics
            growth_metrics = executive_summary['key_metrics']

            # Display key metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                revenue_growth = growth_metrics.get('revenue_growth_rate', 0)
                st.markdown(
                    create_metric_card("Revenue Growth", f"{revenue_growth:.1f}%", revenue_growth),
                    unsafe_allow_html=True
                )

            with col2:
                op_percentage = growth_metrics.get('op_revenue_percentage', 0)
                st.markdown(
                    create_metric_card("OP Revenue Share", f"{op_percentage:.1f}%"),
                    unsafe_allow_html=True
                )

            with col3:
                avg_perf_growth = growth_metrics.get('avg_performance_growth', 0)
                st.markdown(
                    create_metric_card("Avg Performance Growth", f"{avg_perf_growth:.1f}%", avg_perf_growth),
                    unsafe_allow_html=True
                )

            with col4:
                momentum = growth_metrics.get('revenue_momentum', 0)
                st.markdown(
                    create_metric_card("Revenue Momentum", f"{momentum:.1f}%", momentum),
                    unsafe_allow_html=True
                )

            # Insights and recommendations
            st.subheader("💡 Key Insights")
            insights = executive_summary['insights']
            for insight in insights:
                st.markdown(f"""
                <div class="insight-card">
                    {insight}
                </div>
                """, unsafe_allow_html=True)

            # Recommendations
            st.subheader("🎯 Strategic Recommendations")
            recommendations = executive_summary['recommendations']
            for i, rec in enumerate(recommendations, 1):
                st.markdown(f"""
                <div class="insight-card">
                    <strong>{i}.</strong> {rec}
                </div>
                """, unsafe_allow_html=True)

            # Trends analysis
            st.subheader("📊 Trend Analysis")
            trends = executive_summary['trends']

            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown("**🟢 Positive Trends**")
                for trend in trends['positive']:
                    st.success(f"✅ {trend}")

            with col2:
                st.markdown("**🔴 Areas of Concern**")
                for trend in trends['negative']:
                    st.error(f"⚠️ {trend}")

            with col3:
                st.markdown("**🟡 Stable Areas**")
                for trend in trends['neutral']:
                    st.info(f"ℹ️ {trend}")
        
        elif page == "Insights":
            st.header("💡 Observations & Recommendations")
            
            observations = data.get('observations', [])
            
            if observations:
                for i, obs in enumerate(observations, 1):
                    st.markdown(f"""
                    <div class="insight-card">
                        <strong>{i}.</strong> {obs}
                    </div>
                    """, unsafe_allow_html=True)
            else:
                st.info("No specific observations available in the data.")
    
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        st.info("Please ensure the Excel file '3.Monthly Dash Board - June '25.xlsx' is in the current directory.")

if __name__ == "__main__":
    main()
