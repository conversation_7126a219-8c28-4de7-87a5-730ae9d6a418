// Hospital Analytics Dashboard JavaScript

// Global variables
let dashboardData = null;
let charts = {};

// Color scheme for dark theme
const colors = {
    primary: '#3b82f6',
    secondary: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    purple: '#8b5cf6',
    cyan: '#06b6d4',
    text: '#ffffff',
    textSecondary: '#b8c5d6',
    background: '#1e2330'
};

// Chart.js default configuration for dark theme
Chart.defaults.color = colors.text;
Chart.defaults.backgroundColor = colors.background;
Chart.defaults.borderColor = '#374151';
Chart.defaults.plugins.legend.labels.color = colors.text;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    setupNavigation();
    setupEventListeners();
});

// Load data from JSON file
async function loadData() {
    try {
        const response = await fetch('dashboard_data.json');
        dashboardData = await response.json();
        
        // Hide loading screen and show dashboard
        setTimeout(() => {
            document.getElementById('loading-screen').style.opacity = '0';
            setTimeout(() => {
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('dashboard').classList.remove('hidden');
                initializeDashboard();
            }, 500);
        }, 1500);
        
    } catch (error) {
        console.error('Error loading data:', error);
        // Show error message
        document.querySelector('.loading-content h2').textContent = '❌ Error Loading Data';
        document.querySelector('.loading-content p').textContent = 'Please check if dashboard_data.json exists';
    }
}

// Initialize dashboard with data
function initializeDashboard() {
    updateKPIs();
    createCharts();
    populateInsights();
    populateTables();
}

// Update KPI cards
function updateKPIs() {
    const kpis = dashboardData.kpis;
    
    // Total Revenue
    document.getElementById('total-revenue').textContent = formatCurrency(kpis.total_revenue);
    document.getElementById('revenue-change').textContent = `${(kpis.revenue_growth * 100).toFixed(1)}%`;
    document.getElementById('revenue-change').className = `kpi-change ${kpis.revenue_growth >= 0 ? 'positive' : 'negative'}`;
    
    // Total Patients
    const totalPatients = kpis.total_op_patients + kpis.total_ip_patients;
    document.getElementById('total-patients').textContent = formatNumber(totalPatients);
    const patientGrowth = (kpis.op_growth + kpis.ip_growth) / 2;
    document.getElementById('patients-change').textContent = `${(patientGrowth * 100).toFixed(1)}%`;
    document.getElementById('patients-change').className = `kpi-change ${patientGrowth >= 0 ? 'positive' : 'negative'}`;
    
    // Active Departments
    const activeDepartments = dashboardData.specialty_op.length + dashboardData.specialty_ip.length;
    document.getElementById('active-departments').textContent = activeDepartments;
    
    // Growth Rate
    document.getElementById('growth-rate').textContent = `${(kpis.revenue_growth * 100).toFixed(1)}%`;
    document.getElementById('growth-trend').textContent = kpis.revenue_growth >= 0 ? 'Trending Up' : 'Needs Attention';
    document.getElementById('growth-trend').className = `kpi-change ${kpis.revenue_growth >= 0 ? 'positive' : 'negative'}`;
}

// Create all charts
function createCharts() {
    createRevenueChart();
    createPatientsChart();
    createRevenueBreakdownChart();
    createMonthComparisonChart();
    createPerformanceRadarChart();
    createPerformanceTrendChart();
    createPharmacyMarginsChart();
    createMarginTrendsChart();
    createDepartmentChart();
    createDepartmentGrowthChart();
}

// Revenue trends chart
function createRevenueChart() {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueData = dashboardData.revenue_summary.slice(0, 6);
    
    charts.revenue = new Chart(ctx, {
        type: 'line',
        data: {
            labels: revenueData.map(item => item.category),
            datasets: [{
                label: 'May 2025',
                data: revenueData.map(item => item.may_2025),
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                tension: 0.4,
                fill: true
            }, {
                label: 'June 2025',
                data: revenueData.map(item => item.june_2025),
                borderColor: colors.secondary,
                backgroundColor: colors.secondary + '20',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

// Patients volume chart
function createPatientsChart() {
    const ctx = document.getElementById('patientsChart').getContext('2d');
    const opData = dashboardData.specialty_op.slice(0, 8);
    const ipData = dashboardData.specialty_ip.slice(0, 8);
    
    charts.patients = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Outpatient', 'Inpatient'],
            datasets: [{
                data: [
                    opData.reduce((sum, item) => sum + item.current_month, 0),
                    ipData.reduce((sum, item) => sum + item.current_month, 0)
                ],
                backgroundColor: [colors.primary, colors.secondary],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Revenue breakdown chart
function createRevenueBreakdownChart() {
    const ctx = document.getElementById('revenueBreakdownChart').getContext('2d');
    const revenueData = dashboardData.revenue_summary.slice(0, 8);
    
    charts.revenueBreakdown = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: revenueData.map(item => item.subcategory || item.category),
            datasets: [{
                label: 'June 2025 Revenue',
                data: revenueData.map(item => item.june_2025),
                backgroundColor: [
                    colors.primary,
                    colors.secondary,
                    colors.warning,
                    colors.danger,
                    colors.purple,
                    colors.cyan,
                    colors.primary + '80',
                    colors.secondary + '80'
                ],
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45
                    }
                }
            }
        }
    });
}

// Month comparison chart
function createMonthComparisonChart() {
    const ctx = document.getElementById('monthComparisonChart').getContext('2d');
    const revenueData = dashboardData.revenue_summary.slice(0, 6);
    
    charts.monthComparison = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: revenueData.map(item => item.category),
            datasets: [{
                label: 'May 2025',
                data: revenueData.map(item => item.may_2025),
                backgroundColor: colors.primary + '80',
                borderRadius: 4
            }, {
                label: 'June 2025',
                data: revenueData.map(item => item.june_2025),
                backgroundColor: colors.secondary,
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45
                    }
                }
            }
        }
    });
}

// Performance radar chart
function createPerformanceRadarChart() {
    const ctx = document.getElementById('performanceRadarChart').getContext('2d');
    const perfData = dashboardData.performance_report.slice(0, 6);
    
    charts.performanceRadar = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: perfData.map(item => item.metric),
            datasets: [{
                label: 'Current Month',
                data: perfData.map(item => item.current_month),
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                pointBackgroundColor: colors.primary
            }, {
                label: 'Previous Month',
                data: perfData.map(item => item.previous_month),
                borderColor: colors.secondary,
                backgroundColor: colors.secondary + '20',
                pointBackgroundColor: colors.secondary
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    grid: {
                        color: '#374151'
                    },
                    angleLines: {
                        color: '#374151'
                    },
                    pointLabels: {
                        color: colors.text,
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// Performance trend chart
function createPerformanceTrendChart() {
    const ctx = document.getElementById('performanceTrendChart').getContext('2d');
    const perfData = dashboardData.performance_report.slice(0, 8);

    charts.performanceTrend = new Chart(ctx, {
        type: 'line',
        data: {
            labels: perfData.map(item => item.metric),
            datasets: [{
                label: 'Growth Rate',
                data: perfData.map(item => item.variance_ratio * 100),
                borderColor: colors.warning,
                backgroundColor: colors.warning + '20',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45
                    }
                }
            }
        }
    });
}

// Pharmacy margins chart
function createPharmacyMarginsChart() {
    const ctx = document.getElementById('pharmacyMarginsChart').getContext('2d');
    const pharmacyData = dashboardData.pharmacy_margins
        .sort((a, b) => b.june_2025 - a.june_2025)
        .slice(0, 10);

    charts.pharmacyMargins = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: pharmacyData.map(item => item.department),
            datasets: [{
                label: 'June 2025 Margin',
                data: pharmacyData.map(item => item.june_2025 * 100),
                backgroundColor: colors.secondary,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                y: {
                    ticks: {
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// Margin trends chart
function createMarginTrendsChart() {
    const ctx = document.getElementById('marginTrendsChart').getContext('2d');
    const topDepartments = dashboardData.pharmacy_margins
        .sort((a, b) => b.june_2025 - a.june_2025)
        .slice(0, 5);

    charts.marginTrends = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['April', 'May', 'June'],
            datasets: topDepartments.map((dept, index) => ({
                label: dept.department,
                data: [dept.apr_2025 * 100, dept.may_2025 * 100, dept.june_2025 * 100],
                borderColor: [colors.primary, colors.secondary, colors.warning, colors.danger, colors.purple][index],
                backgroundColor: [colors.primary, colors.secondary, colors.warning, colors.danger, colors.purple][index] + '20',
                tension: 0.4
            }))
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 10
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

// Department chart (OP/IP)
function createDepartmentChart() {
    const ctx = document.getElementById('departmentChart').getContext('2d');
    const opData = dashboardData.specialty_op
        .sort((a, b) => b.current_month - a.current_month)
        .slice(0, 10);

    charts.department = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: opData.map(item => item.department),
            datasets: [{
                label: 'Current Month',
                data: opData.map(item => item.current_month),
                backgroundColor: colors.primary,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// Department growth chart
function createDepartmentGrowthChart() {
    const ctx = document.getElementById('departmentGrowthChart').getContext('2d');
    const growthData = dashboardData.specialty_op
        .filter(item => item.variance_ratio !== 0)
        .sort((a, b) => Math.abs(b.variance_ratio) - Math.abs(a.variance_ratio))
        .slice(0, 8);

    charts.departmentGrowth = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: growthData.map(item => item.department),
            datasets: [{
                label: 'Growth Rate',
                data: growthData.map(item => item.variance_ratio * 100),
                backgroundColor: growthData.map(item =>
                    item.variance_ratio >= 0 ? colors.secondary : colors.danger
                ),
                borderRadius: 4,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// Setup navigation
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const sections = document.querySelectorAll('.content-section');

    navItems.forEach(item => {
        item.addEventListener('click', () => {
            const targetSection = item.dataset.section;

            // Update active nav item
            navItems.forEach(nav => nav.classList.remove('active'));
            item.classList.add('active');

            // Show target section
            sections.forEach(section => {
                section.classList.remove('active');
                if (section.id === targetSection) {
                    section.classList.add('active');
                }
            });
        });
    });
}

// Setup event listeners
function setupEventListeners() {
    // Department tabs
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tab = btn.dataset.tab;

            // Update active tab
            tabBtns.forEach(t => t.classList.remove('active'));
            btn.classList.add('active');

            // Update department chart
            updateDepartmentChart(tab);
        });
    });

    // Chart controls
    const chartBtns = document.querySelectorAll('.chart-btn');
    chartBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const parent = btn.closest('.chart-controls');
            parent.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });
}

// Update department chart based on tab
function updateDepartmentChart(type) {
    const ctx = document.getElementById('departmentChart').getContext('2d');
    const title = document.getElementById('dept-chart-title');

    let data, chartTitle;

    if (type === 'op') {
        data = dashboardData.specialty_op
            .sort((a, b) => b.current_month - a.current_month)
            .slice(0, 10);
        chartTitle = 'Top Outpatient Departments';
    } else {
        data = dashboardData.specialty_ip
            .sort((a, b) => b.current_month - a.current_month)
            .slice(0, 10);
        chartTitle = 'Top Inpatient Departments';
    }

    title.textContent = chartTitle;

    // Destroy existing chart and create new one
    if (charts.department) {
        charts.department.destroy();
    }

    charts.department = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.department),
            datasets: [{
                label: 'Current Month',
                data: data.map(item => item.current_month),
                backgroundColor: type === 'op' ? colors.primary : colors.secondary,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// Populate insights
function populateInsights() {
    // Quick insights for overview
    const quickInsights = document.getElementById('quick-insights');
    const insights = generateInsights();

    quickInsights.innerHTML = insights.slice(0, 4).map(insight => `
        <div class="insight-item ${insight.type}">
            <i class="fas ${insight.icon}"></i>
            <p>${insight.text}</p>
        </div>
    `).join('');

    // Detailed insights for insights page
    const keyFindings = document.getElementById('key-findings');
    const recommendations = document.getElementById('recommendations');
    const observations = document.getElementById('detailed-observations');

    if (keyFindings) {
        keyFindings.innerHTML = insights.filter(i => i.category === 'finding').map(insight => `
            <div class="insight-card finding">
                <i class="fas ${insight.icon}"></i>
                <p>${insight.text}</p>
            </div>
        `).join('');
    }

    if (recommendations) {
        recommendations.innerHTML = generateRecommendations().map(rec => `
            <div class="insight-card recommendation">
                <i class="fas fa-target"></i>
                <p>${rec}</p>
            </div>
        `).join('');
    }

    if (observations) {
        observations.innerHTML = dashboardData.observations.map((obs, index) => `
            <div class="observation-item">
                <strong>${index + 1}.</strong> ${obs}
            </div>
        `).join('');
    }
}

// Generate insights based on data
function generateInsights() {
    const insights = [];
    const kpis = dashboardData.kpis;

    // Revenue insights
    if (kpis.revenue_growth > 0.05) {
        insights.push({
            type: 'positive',
            category: 'finding',
            icon: 'fa-chart-line',
            text: `Strong revenue growth of ${(kpis.revenue_growth * 100).toFixed(1)}% observed this month`
        });
    } else if (kpis.revenue_growth < -0.05) {
        insights.push({
            type: 'danger',
            category: 'finding',
            icon: 'fa-exclamation-triangle',
            text: `Revenue declined by ${Math.abs(kpis.revenue_growth * 100).toFixed(1)}% - immediate attention needed`
        });
    }

    // Patient volume insights
    const totalPatients = kpis.total_op_patients + kpis.total_ip_patients;
    if (totalPatients > 50000) {
        insights.push({
            type: 'positive',
            category: 'finding',
            icon: 'fa-users',
            text: `High patient volume of ${formatNumber(totalPatients)} indicates strong market presence`
        });
    }

    // Department performance
    const topOPDept = dashboardData.specialty_op.reduce((max, dept) =>
        dept.current_month > max.current_month ? dept : max
    );
    insights.push({
        type: 'positive',
        category: 'finding',
        icon: 'fa-star',
        text: `${topOPDept.department} leads OP services with ${formatNumber(topOPDept.current_month)} patients`
    });

    // Pharmacy performance
    const avgMargin = dashboardData.pharmacy_margins.reduce((sum, dept) =>
        sum + dept.june_2025, 0) / dashboardData.pharmacy_margins.length;

    if (avgMargin > 0.25) {
        insights.push({
            type: 'positive',
            category: 'finding',
            icon: 'fa-pills',
            text: `Pharmacy margins are healthy at ${(avgMargin * 100).toFixed(1)}% average`
        });
    } else {
        insights.push({
            type: 'warning',
            category: 'finding',
            icon: 'fa-exclamation-circle',
            text: `Pharmacy margins need improvement - currently at ${(avgMargin * 100).toFixed(1)}%`
        });
    }

    return insights;
}

// Generate recommendations
function generateRecommendations() {
    const recommendations = [];
    const kpis = dashboardData.kpis;

    // Revenue recommendations
    if (kpis.revenue_growth < 0) {
        recommendations.push('Implement revenue recovery strategies and cost optimization measures');
        recommendations.push('Focus on high-margin services and patient retention programs');
    }

    // Department recommendations
    const lowPerformingDepts = dashboardData.specialty_op.filter(dept =>
        dept.variance_ratio < -0.1
    );

    if (lowPerformingDepts.length > 0) {
        recommendations.push(`Address performance issues in ${lowPerformingDepts.length} underperforming departments`);
    }

    // Pharmacy recommendations
    const lowMarginDepts = dashboardData.pharmacy_margins.filter(dept =>
        dept.june_2025 < 0.15
    );

    if (lowMarginDepts.length > 0) {
        recommendations.push('Optimize pharmacy pricing and procurement for low-margin departments');
    }

    // Growth recommendations
    const highGrowthDepts = dashboardData.specialty_op.filter(dept =>
        dept.variance_ratio > 0.2
    );

    if (highGrowthDepts.length > 0) {
        recommendations.push(`Scale successful strategies from ${highGrowthDepts[0].department} to other departments`);
    }

    // General recommendations
    recommendations.push('Implement data-driven decision making across all departments');
    recommendations.push('Enhance patient experience and satisfaction programs');
    recommendations.push('Invest in technology and digital health solutions');

    return recommendations;
}

// Populate tables
function populateTables() {
    // Revenue table
    const revenueTable = document.getElementById('revenue-table');
    if (revenueTable) {
        const tbody = revenueTable.querySelector('tbody');
        tbody.innerHTML = dashboardData.revenue_summary.map(item => `
            <tr>
                <td>${item.category}</td>
                <td>${formatCurrency(item.may_2025)}</td>
                <td>${formatCurrency(item.june_2025)}</td>
                <td class="${item.variance_amount >= 0 ? 'text-success' : 'text-danger'}">
                    ${formatCurrency(item.variance_amount)}
                </td>
                <td class="${item.variance_ratio >= 0 ? 'text-success' : 'text-danger'}">
                    ${(item.variance_ratio * 100).toFixed(1)}%
                </td>
            </tr>
        `).join('');
    }
}

// Utility functions
function formatCurrency(value) {
    if (value >= 10000000) {
        return '₹' + (value / 10000000).toFixed(1) + 'Cr';
    } else if (value >= 100000) {
        return '₹' + (value / 100000).toFixed(1) + 'L';
    } else if (value >= 1000) {
        return '₹' + (value / 1000).toFixed(1) + 'K';
    }
    return '₹' + value.toFixed(0);
}

function formatNumber(value) {
    if (value >= 1000000) {
        return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
        return (value / 1000).toFixed(1) + 'K';
    }
    return value.toString();
}

// Refresh data function
function refreshData() {
    const refreshBtn = document.querySelector('.refresh-btn');
    const icon = refreshBtn.querySelector('i');

    // Add spinning animation
    icon.style.animation = 'spin 1s linear infinite';

    // Simulate data refresh
    setTimeout(() => {
        icon.style.animation = '';

        // Show success message
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-check"></i> Updated';
        refreshBtn.style.background = 'var(--gradient-secondary)';

        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.style.background = 'var(--gradient-primary)';
        }, 2000);
    }, 1000);
}

// Export functions for global access
window.refreshData = refreshData;
