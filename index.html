<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 Hospital Analytics Dashboard - Believers Church Medical College</title>
    
    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2>🏥 Loading Hospital Analytics Dashboard</h2>
            <p>Preparing your data insights...</p>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard" class="dashboard hidden">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-hospital-alt"></i>
                    <div class="logo-text">
                        <h1>Hospital Analytics Dashboard</h1>
                        <p>Believers Church Medical College Hospital</p>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="date-info">
                        <i class="fas fa-calendar-alt"></i>
                        <span>June 2025 Report</span>
                    </div>
                    <button class="refresh-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="sidebar">
            <div class="nav-menu">
                <button class="nav-item active" data-section="overview">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Overview</span>
                </button>
                <button class="nav-item" data-section="revenue">
                    <i class="fas fa-chart-line"></i>
                    <span>Revenue Analysis</span>
                </button>
                <button class="nav-item" data-section="performance">
                    <i class="fas fa-chart-bar"></i>
                    <span>Performance</span>
                </button>
                <button class="nav-item" data-section="pharmacy">
                    <i class="fas fa-pills"></i>
                    <span>Pharmacy</span>
                </button>
                <button class="nav-item" data-section="departments">
                    <i class="fas fa-hospital"></i>
                    <span>Departments</span>
                </button>
                <button class="nav-item" data-section="insights">
                    <i class="fas fa-lightbulb"></i>
                    <span>Insights</span>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <div class="section-header">
                    <h2><i class="fas fa-tachometer-alt"></i> Dashboard Overview</h2>
                    <p>Key performance indicators and metrics for June 2025</p>
                </div>

                <!-- KPI Cards -->
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-icon revenue">
                            <i class="fas fa-rupee-sign"></i>
                        </div>
                        <div class="kpi-content">
                            <h3 id="total-revenue">₹0</h3>
                            <p>Total Revenue</p>
                            <span class="kpi-change positive" id="revenue-change">+0%</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-icon patients">
                            <i class="fas fa-user-injured"></i>
                        </div>
                        <div class="kpi-content">
                            <h3 id="total-patients">0</h3>
                            <p>Total Patients</p>
                            <span class="kpi-change positive" id="patients-change">+0%</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-icon departments">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <div class="kpi-content">
                            <h3 id="active-departments">0</h3>
                            <p>Active Departments</p>
                            <span class="kpi-change neutral">Stable</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-icon growth">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="kpi-content">
                            <h3 id="growth-rate">0%</h3>
                            <p>Growth Rate</p>
                            <span class="kpi-change positive" id="growth-trend">Trending Up</span>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="charts-grid">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Revenue Trends</h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" data-period="monthly">Monthly</button>
                                <button class="chart-btn" data-period="quarterly">Quarterly</button>
                            </div>
                        </div>
                        <canvas id="revenueChart"></canvas>
                    </div>

                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Patient Volume</h3>
                            <div class="chart-legend">
                                <span class="legend-item op">OP</span>
                                <span class="legend-item ip">IP</span>
                            </div>
                        </div>
                        <canvas id="patientsChart"></canvas>
                    </div>
                </div>

                <!-- Quick Insights -->
                <div class="insights-preview">
                    <h3><i class="fas fa-lightbulb"></i> Quick Insights</h3>
                    <div class="insights-grid" id="quick-insights">
                        <!-- Insights will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Revenue Analysis Section -->
            <section id="revenue" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-line"></i> Revenue Analysis</h2>
                    <p>Detailed revenue breakdown and trends</p>
                </div>

                <div class="charts-grid">
                    <div class="chart-container large">
                        <div class="chart-header">
                            <h3>Revenue Breakdown</h3>
                            <select id="revenue-filter" class="chart-select">
                                <option value="all">All Categories</option>
                                <option value="op">Outpatient</option>
                                <option value="ip">Inpatient</option>
                            </select>
                        </div>
                        <canvas id="revenueBreakdownChart"></canvas>
                    </div>

                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Month-over-Month Comparison</h3>
                        </div>
                        <canvas id="monthComparisonChart"></canvas>
                    </div>
                </div>

                <div class="data-table-container">
                    <h3>Revenue Details</h3>
                    <div class="table-wrapper">
                        <table id="revenue-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>May 2025</th>
                                    <th>June 2025</th>
                                    <th>Variance</th>
                                    <th>Growth %</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Performance Section -->
            <section id="performance" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> Performance Metrics</h2>
                    <p>Key performance indicators and operational metrics</p>
                </div>

                <div class="charts-grid">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Performance Radar</h3>
                        </div>
                        <canvas id="performanceRadarChart"></canvas>
                    </div>

                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Trend Analysis</h3>
                        </div>
                        <canvas id="performanceTrendChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Pharmacy Section -->
            <section id="pharmacy" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-pills"></i> Pharmacy Analysis</h2>
                    <p>Pharmacy gross margins and department performance</p>
                </div>

                <div class="charts-grid">
                    <div class="chart-container large">
                        <div class="chart-header">
                            <h3>Department Margins</h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" data-view="top10">Top 10</button>
                                <button class="chart-btn" data-view="all">All Departments</button>
                            </div>
                        </div>
                        <canvas id="pharmacyMarginsChart"></canvas>
                    </div>

                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Margin Trends</h3>
                        </div>
                        <canvas id="marginTrendsChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Departments Section -->
            <section id="departments" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-hospital"></i> Department Analysis</h2>
                    <p>Specialty-wise outpatient and inpatient analysis</p>
                </div>

                <div class="department-tabs">
                    <button class="tab-btn active" data-tab="op">Outpatient</button>
                    <button class="tab-btn" data-tab="ip">Inpatient</button>
                </div>

                <div class="charts-grid">
                    <div class="chart-container large">
                        <div class="chart-header">
                            <h3 id="dept-chart-title">Top Outpatient Departments</h3>
                        </div>
                        <canvas id="departmentChart"></canvas>
                    </div>

                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Growth Analysis</h3>
                        </div>
                        <canvas id="departmentGrowthChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Insights Section -->
            <section id="insights" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-lightbulb"></i> Insights & Recommendations</h2>
                    <p>AI-powered insights and strategic recommendations</p>
                </div>

                <div class="insights-container">
                    <div class="insights-column">
                        <h3><i class="fas fa-chart-line"></i> Key Findings</h3>
                        <div id="key-findings" class="insights-list">
                            <!-- Populated by JavaScript -->
                        </div>
                    </div>

                    <div class="insights-column">
                        <h3><i class="fas fa-target"></i> Recommendations</h3>
                        <div id="recommendations" class="insights-list">
                            <!-- Populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="observations-section">
                    <h3><i class="fas fa-clipboard-list"></i> Detailed Observations</h3>
                    <div id="detailed-observations" class="observations-list">
                        <!-- Populated by JavaScript -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="dashboard.js"></script>
</body>
</html>
