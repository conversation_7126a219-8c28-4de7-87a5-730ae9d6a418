import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import re

class HospitalDataProcessor:
    """Process and clean hospital dashboard data from Excel file"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.excel_file = pd.ExcelFile(file_path)
        self.processed_data = {}
        
    def load_all_sheets(self) -> Dict[str, pd.DataFrame]:
        """Load all sheets from the Excel file"""
        sheets = {}
        for sheet_name in self.excel_file.sheet_names:
            sheets[sheet_name] = pd.read_excel(self.file_path, sheet_name=sheet_name)
        return sheets
    
    def clean_revenue_summary(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and process Revenue Summary sheet"""
        # Find the actual data start row
        start_row = None
        for idx, row in df.iterrows():
            if 'PARTICULARS' in str(row.iloc[1]).upper():
                start_row = idx + 1
                break
        
        if start_row is None:
            return pd.DataFrame()
        
        # Extract relevant data
        data_df = df.iloc[start_row:].copy()
        
        # Clean column names
        columns = ['Category', 'Subcategory', 'Description', 'May_25', 'June_25', 'Variance_Amount', 'Variance_Ratio']
        if len(data_df.columns) >= len(columns):
            data_df = data_df.iloc[:, :len(columns)]
            data_df.columns = columns
        
        # Remove empty rows
        data_df = data_df.dropna(subset=['Category', 'Subcategory'], how='all')
        
        # Convert numeric columns
        numeric_cols = ['May_25', 'June_25', 'Variance_Amount', 'Variance_Ratio']
        for col in numeric_cols:
            if col in data_df.columns:
                data_df[col] = pd.to_numeric(data_df[col], errors='coerce')
        
        return data_df
    
    def clean_financial_summary(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and process Financial Summary sheet"""
        # Similar processing as revenue summary
        start_row = None
        for idx, row in df.iterrows():
            if 'PARTICULARS' in str(row.iloc[1]).upper():
                start_row = idx + 1
                break
        
        if start_row is None:
            return pd.DataFrame()
        
        data_df = df.iloc[start_row:].copy()
        
        # Clean and standardize
        columns = ['Category', 'Description', 'Previous_Month', 'Current_Month', 'Variance_Amount', 'Variance_Ratio']
        if len(data_df.columns) >= len(columns):
            data_df = data_df.iloc[:, :len(columns)]
            data_df.columns = columns
        
        data_df = data_df.dropna(subset=['Category'], how='all')
        
        # Convert numeric columns
        numeric_cols = ['Previous_Month', 'Current_Month', 'Variance_Amount', 'Variance_Ratio']
        for col in numeric_cols:
            if col in data_df.columns:
                data_df[col] = pd.to_numeric(data_df[col], errors='coerce')
        
        return data_df
    
    def clean_performance_report(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and process Performance Report sheet"""
        start_row = None
        for idx, row in df.iterrows():
            if 'PARTICULARS' in str(row.iloc[1]).upper():
                start_row = idx + 1
                break
        
        if start_row is None:
            return pd.DataFrame()
        
        data_df = df.iloc[start_row:].copy()
        
        columns = ['Category', 'Metric', 'Previous_Month', 'Current_Month', 'Variance_Amount', 'Variance_Ratio']
        if len(data_df.columns) >= len(columns):
            data_df = data_df.iloc[:, :len(columns)]
            data_df.columns = columns
        
        data_df = data_df.dropna(subset=['Category'], how='all')
        
        # Convert numeric columns
        numeric_cols = ['Previous_Month', 'Current_Month', 'Variance_Amount', 'Variance_Ratio']
        for col in numeric_cols:
            if col in data_df.columns:
                data_df[col] = pd.to_numeric(data_df[col], errors='coerce')
        
        return data_df
    
    def clean_pharmacy_margins(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and process Pharmacy Gross Margin sheet"""
        # Find header row
        start_row = None
        for idx, row in df.iterrows():
            if 'ADVISING SPECALISATION' in str(row.iloc[1]).upper():
                start_row = idx
                break
        
        if start_row is None:
            return pd.DataFrame()
        
        # Extract data
        headers = df.iloc[start_row].values
        data_df = df.iloc[start_row + 1:].copy()
        data_df.columns = ['Department', 'Apr_25', 'May_25', 'June_25', 'Extra'][:len(data_df.columns)]
        
        # Remove empty rows
        data_df = data_df.dropna(subset=['Department'])
        
        # Convert margin columns to numeric
        margin_cols = ['Apr_25', 'May_25', 'June_25']
        for col in margin_cols:
            if col in data_df.columns:
                data_df[col] = pd.to_numeric(data_df[col], errors='coerce')
        
        return data_df
    
    def clean_specialty_data(self, df: pd.DataFrame, sheet_type: str) -> pd.DataFrame:
        """Clean specialty-wise OP/IP data"""
        # Find the header row
        start_row = None
        for idx, row in df.iterrows():
            if 'Sl No' in str(row.iloc[0]) or 'depT' in str(row.iloc[1]):
                start_row = idx
                break
        
        if start_row is None:
            return pd.DataFrame()
        
        # Extract data
        data_df = df.iloc[start_row + 1:].copy()
        
        # Set appropriate column names based on sheet type
        if sheet_type == 'OP':
            columns = ['Sl_No', 'Department', 'Unit', 'Previous_Month', 'Current_Month', 'Variance_Amount', 'Variance_Ratio']
        else:  # IP
            columns = ['Sl_No', 'Department', 'Unit', 'Previous_Month', 'Current_Month', 'Variance_Amount', 'Variance_Ratio', 'Extra']
        
        if len(data_df.columns) >= len(columns):
            data_df = data_df.iloc[:, :len(columns)]
            data_df.columns = columns
        
        # Remove empty rows
        data_df = data_df.dropna(subset=['Sl_No'], how='all')
        
        # Convert numeric columns
        numeric_cols = ['Previous_Month', 'Current_Month', 'Variance_Amount', 'Variance_Ratio']
        for col in numeric_cols:
            if col in data_df.columns:
                data_df[col] = pd.to_numeric(data_df[col], errors='coerce')
        
        return data_df
    
    def extract_observations(self, df: pd.DataFrame) -> List[str]:
        """Extract observations and recommendations"""
        observations = []
        for idx, row in df.iterrows():
            if pd.notna(row.iloc[1]) and len(str(row.iloc[1])) > 10:
                observations.append(str(row.iloc[1]))
        return observations
    
    def process_all_data(self) -> Dict[str, Any]:
        """Process all sheets and return cleaned data"""
        sheets = self.load_all_sheets()
        
        processed = {
            'revenue_summary': self.clean_revenue_summary(sheets.get('Revenue Summary', pd.DataFrame())),
            'financial_summary': self.clean_financial_summary(sheets.get('Financial Summary', pd.DataFrame())),
            'performance_report': self.clean_performance_report(sheets.get('Performance Report', pd.DataFrame())),
            'pharmacy_margins': self.clean_pharmacy_margins(sheets.get('Pharmacy - Gross Margin Ratio', pd.DataFrame())),
            'specialty_op': self.clean_specialty_data(sheets.get('Specalitywise OP', pd.DataFrame()), 'OP'),
            'specialty_ip': self.clean_specialty_data(sheets.get('Specalitywise IP', pd.DataFrame()), 'IP'),
            'observations': self.extract_observations(sheets.get('Observations & Recommendations', pd.DataFrame()))
        }
        
        self.processed_data = processed
        return processed
    
    def get_key_metrics(self) -> Dict[str, float]:
        """Calculate key performance metrics"""
        metrics = {}
        
        # Revenue metrics
        if not self.processed_data['revenue_summary'].empty:
            revenue_df = self.processed_data['revenue_summary']
            
            # Total revenue
            op_revenue = revenue_df[revenue_df['Category'].str.contains('OP Revenue', na=False)]['June_25'].sum()
            ip_revenue = revenue_df[revenue_df['Category'].str.contains('IP Revenue', na=False)]['June_25'].sum()
            
            metrics['total_revenue'] = op_revenue + ip_revenue
            metrics['op_revenue'] = op_revenue
            metrics['ip_revenue'] = ip_revenue
            
            # Growth rates
            revenue_variance = revenue_df['Variance_Ratio'].mean()
            metrics['revenue_growth'] = revenue_variance if pd.notna(revenue_variance) else 0
        
        # Performance metrics
        if not self.processed_data['performance_report'].empty:
            perf_df = self.processed_data['performance_report']
            
            # Patient metrics
            op_patients = perf_df[perf_df['Metric'].str.contains('OP', na=False)]['Current_Month'].sum()
            ip_patients = perf_df[perf_df['Metric'].str.contains('IP', na=False)]['Current_Month'].sum()
            
            metrics['op_patients'] = op_patients if pd.notna(op_patients) else 0
            metrics['ip_patients'] = ip_patients if pd.notna(ip_patients) else 0
        
        return metrics
