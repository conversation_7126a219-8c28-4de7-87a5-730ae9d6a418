import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np

# Dark theme configuration
DARK_THEME = {
    'template': 'plotly_dark',
    'paper_bgcolor': '#0e1117',
    'plot_bgcolor': '#1e2130',
    'font_color': '#f8fafc',
    'grid_color': '#374151'
}

COLOR_PALETTE = {
    'primary': '#3b82f6',
    'secondary': '#10b981',
    'accent': '#f59e0b',
    'danger': '#ef4444',
    'success': '#22c55e',
    'warning': '#f97316',
    'info': '#06b6d4',
    'purple': '#8b5cf6'
}

def apply_dark_theme(fig):
    """Apply consistent dark theme to plotly figures"""
    fig.update_layout(
        template='plotly_dark',
        paper_bgcolor='#0e1117',
        plot_bgcolor='#1e2130',
        font_color='#f8fafc',
        title_font_size=20,
        title_font_color='#f8fafc',
        legend=dict(
            bgcolor='rgba(30, 33, 48, 0.8)',
            bordercolor='#374151',
            borderwidth=1
        ),
        xaxis=dict(
            gridcolor='#374151',
            linecolor='#6b7280'
        ),
        yaxis=dict(
            gridcolor='#374151',
            linecolor='#6b7280'
        )
    )
    return fig

def create_revenue_waterfall(data):
    """Create a waterfall chart for revenue analysis"""
    if data.empty:
        return go.Figure()
    
    # Extract revenue components
    revenue_items = data[data['Category'].str.contains('Revenue', na=False)]
    
    if revenue_items.empty:
        return go.Figure()
    
    # Prepare waterfall data
    categories = revenue_items['Subcategory'].tolist()
    values = revenue_items['June_25'].tolist()
    
    fig = go.Figure(go.Waterfall(
        name="Revenue Breakdown",
        orientation="v",
        measure=["relative"] * len(categories),
        x=categories,
        textposition="outside",
        text=[f"₹{v:,.0f}" for v in values],
        y=values,
        connector={"line": {"color": COLOR_PALETTE['primary']}},
        increasing={"marker": {"color": COLOR_PALETTE['success']}},
        decreasing={"marker": {"color": COLOR_PALETTE['danger']}},
        totals={"marker": {"color": COLOR_PALETTE['info']}}
    ))
    
    fig.update_layout(
        title="Revenue Waterfall Analysis - June 2025",
        xaxis_title="Revenue Components",
        yaxis_title="Amount (₹)",
        height=600
    )
    
    return apply_dark_theme(fig)

def create_performance_radar(data):
    """Create radar chart for performance metrics"""
    if data.empty:
        return go.Figure()
    
    # Select key performance metrics
    perf_metrics = data.dropna(subset=['Current_Month', 'Previous_Month']).head(8)
    
    if perf_metrics.empty:
        return go.Figure()
    
    categories = perf_metrics['Metric'].tolist()
    current_values = perf_metrics['Current_Month'].tolist()
    previous_values = perf_metrics['Previous_Month'].tolist()
    
    # Normalize values for radar chart
    max_val = max(max(current_values), max(previous_values))
    current_normalized = [v/max_val * 100 for v in current_values]
    previous_normalized = [v/max_val * 100 for v in previous_values]
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=current_normalized,
        theta=categories,
        fill='toself',
        name='Current Month',
        line_color=COLOR_PALETTE['primary'],
        fillcolor=f"rgba(59, 130, 246, 0.3)"
    ))
    
    fig.add_trace(go.Scatterpolar(
        r=previous_normalized,
        theta=categories,
        fill='toself',
        name='Previous Month',
        line_color=COLOR_PALETTE['secondary'],
        fillcolor=f"rgba(16, 185, 129, 0.3)"
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100],
                gridcolor='#374151'
            ),
            angularaxis=dict(
                gridcolor='#374151'
            )
        ),
        title="Performance Metrics Comparison",
        height=600
    )
    
    return apply_dark_theme(fig)

def create_department_heatmap(pharmacy_data):
    """Create heatmap for department performance"""
    if pharmacy_data.empty:
        return go.Figure()
    
    # Prepare data for heatmap
    departments = pharmacy_data['Department'].head(20).tolist()
    months = ['Apr_25', 'May_25', 'June_25']
    month_labels = ['April', 'May', 'June']
    
    # Create matrix
    z_data = []
    for month in months:
        z_data.append(pharmacy_data[month].head(20).tolist())
    
    fig = go.Figure(data=go.Heatmap(
        z=z_data,
        x=departments,
        y=month_labels,
        colorscale='Viridis',
        text=[[f"{val:.2%}" for val in row] for row in z_data],
        texttemplate="%{text}",
        textfont={"size": 10},
        hoverongaps=False,
        colorbar=dict(title="Margin %")
    ))
    
    fig.update_layout(
        title="Department Margin Heatmap",
        xaxis_title="Departments",
        yaxis_title="Months",
        height=500
    )
    
    fig.update_xaxes(tickangle=-45)
    
    return apply_dark_theme(fig)

def create_trend_analysis(data):
    """Create comprehensive trend analysis"""
    if data.empty:
        return go.Figure()
    
    # Extract trend data
    trend_data = data.dropna(subset=['Previous_Month', 'Current_Month']).head(10)
    
    if trend_data.empty:
        return go.Figure()
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Growth Trends', 'Variance Analysis', 'Performance Distribution', 'Top Performers'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # Growth trends
    categories = trend_data['Metric'].tolist()
    growth_rates = ((trend_data['Current_Month'] - trend_data['Previous_Month']) / 
                   trend_data['Previous_Month'] * 100).tolist()
    
    fig.add_trace(
        go.Bar(
            x=categories,
            y=growth_rates,
            name='Growth Rate %',
            marker_color=[COLOR_PALETTE['success'] if x > 0 else COLOR_PALETTE['danger'] for x in growth_rates]
        ),
        row=1, col=1
    )
    
    # Variance analysis
    variances = trend_data['Variance_Ratio'].dropna().tolist()
    fig.add_trace(
        go.Scatter(
            x=list(range(len(variances))),
            y=variances,
            mode='lines+markers',
            name='Variance Trend',
            line=dict(color=COLOR_PALETTE['primary'], width=3),
            marker=dict(size=8)
        ),
        row=1, col=2
    )
    
    # Performance distribution
    current_values = trend_data['Current_Month'].tolist()
    fig.add_trace(
        go.Histogram(
            x=current_values,
            name='Performance Distribution',
            marker_color=COLOR_PALETTE['accent'],
            opacity=0.7
        ),
        row=2, col=1
    )
    
    # Top performers
    top_performers = trend_data.nlargest(5, 'Current_Month')
    fig.add_trace(
        go.Bar(
            x=top_performers['Metric'],
            y=top_performers['Current_Month'],
            name='Top Performers',
            marker_color=COLOR_PALETTE['purple'],
            text=top_performers['Current_Month'],
            texttemplate='%{text:,.0f}',
            textposition='outside'
        ),
        row=2, col=2
    )
    
    fig.update_layout(
        title="Comprehensive Trend Analysis",
        height=800,
        showlegend=False
    )
    
    return apply_dark_theme(fig)

def create_financial_sunburst(revenue_data, financial_data):
    """Create sunburst chart for financial breakdown"""
    if revenue_data.empty and financial_data.empty:
        return go.Figure()
    
    # Prepare hierarchical data
    labels = []
    parents = []
    values = []
    
    # Root
    labels.append("Total Finance")
    parents.append("")
    values.append(0)
    
    # Revenue branch
    if not revenue_data.empty:
        labels.append("Revenue")
        parents.append("Total Finance")
        revenue_total = revenue_data['June_25'].sum()
        values.append(revenue_total)
        
        # Revenue subcategories
        for _, row in revenue_data.head(8).iterrows():
            if pd.notna(row['June_25']) and row['June_25'] > 0:
                labels.append(row['Subcategory'])
                parents.append("Revenue")
                values.append(row['June_25'])
    
    # Financial branch
    if not financial_data.empty:
        labels.append("Other Financial")
        parents.append("Total Finance")
        financial_total = financial_data['Current_Month'].sum()
        values.append(financial_total)
        
        # Financial subcategories
        for _, row in financial_data.head(6).iterrows():
            if pd.notna(row['Current_Month']) and row['Current_Month'] > 0:
                labels.append(row['Description'])
                parents.append("Other Financial")
                values.append(row['Current_Month'])
    
    fig = go.Figure(go.Sunburst(
        labels=labels,
        parents=parents,
        values=values,
        branchvalues="total",
        hovertemplate='<b>%{label}</b><br>Value: ₹%{value:,.0f}<extra></extra>',
        maxdepth=3
    ))
    
    fig.update_layout(
        title="Financial Structure Analysis",
        height=600
    )
    
    return apply_dark_theme(fig)

def create_kpi_gauge(value, title, max_value=None, target=None):
    """Create a gauge chart for KPI visualization"""
    if max_value is None:
        max_value = value * 1.5
    
    fig = go.Figure(go.Indicator(
        mode="gauge+number+delta",
        value=value,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': title},
        delta={'reference': target if target else value * 0.9},
        gauge={
            'axis': {'range': [None, max_value]},
            'bar': {'color': COLOR_PALETTE['primary']},
            'steps': [
                {'range': [0, max_value * 0.5], 'color': "lightgray"},
                {'range': [max_value * 0.5, max_value * 0.8], 'color': "gray"}
            ],
            'threshold': {
                'line': {'color': COLOR_PALETTE['danger'], 'width': 4},
                'thickness': 0.75,
                'value': target if target else max_value * 0.9
            }
        }
    ))
    
    fig.update_layout(height=400)
    return apply_dark_theme(fig)

def create_comparative_analysis(op_data, ip_data):
    """Create comparative analysis between OP and IP"""
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Volume Comparison', 'Growth Rates', 'Department Performance', 'Variance Analysis'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # Volume comparison
    if not op_data.empty and not ip_data.empty:
        op_total = op_data['Current_Month'].sum()
        ip_total = ip_data['Current_Month'].sum()
        
        fig.add_trace(
            go.Bar(
                x=['Outpatient', 'Inpatient'],
                y=[op_total, ip_total],
                name='Patient Volume',
                marker_color=[COLOR_PALETTE['primary'], COLOR_PALETTE['secondary']],
                text=[f'{op_total:,.0f}', f'{ip_total:,.0f}'],
                textposition='outside'
            ),
            row=1, col=1
        )
    
    # Growth rates
    if not op_data.empty:
        op_growth = op_data['Variance_Ratio'].dropna().head(10)
        fig.add_trace(
            go.Scatter(
                x=list(range(len(op_growth))),
                y=op_growth,
                mode='lines+markers',
                name='OP Growth',
                line=dict(color=COLOR_PALETTE['primary'])
            ),
            row=1, col=2
        )
    
    if not ip_data.empty:
        ip_growth = ip_data['Variance_Ratio'].dropna().head(10)
        fig.add_trace(
            go.Scatter(
                x=list(range(len(ip_growth))),
                y=ip_growth,
                mode='lines+markers',
                name='IP Growth',
                line=dict(color=COLOR_PALETTE['secondary'])
            ),
            row=1, col=2
        )
    
    # Department performance
    if not op_data.empty:
        top_op = op_data.nlargest(8, 'Current_Month')
        fig.add_trace(
            go.Bar(
                x=top_op['Department'],
                y=top_op['Current_Month'],
                name='OP Departments',
                marker_color=COLOR_PALETTE['accent']
            ),
            row=2, col=1
        )
    
    # Variance analysis
    if not ip_data.empty:
        ip_variance = ip_data.dropna(subset=['Variance_Amount']).head(8)
        fig.add_trace(
            go.Bar(
                x=ip_variance['Department'],
                y=ip_variance['Variance_Amount'],
                name='IP Variance',
                marker_color=[COLOR_PALETTE['success'] if x > 0 else COLOR_PALETTE['danger'] 
                            for x in ip_variance['Variance_Amount']]
            ),
            row=2, col=2
        )
    
    fig.update_layout(
        title="OP vs IP Comparative Analysis",
        height=800,
        showlegend=True
    )
    
    fig.update_xaxes(tickangle=-45, row=2, col=1)
    fig.update_xaxes(tickangle=-45, row=2, col=2)
    
    return apply_dark_theme(fig)
