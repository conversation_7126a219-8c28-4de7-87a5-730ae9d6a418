#!/usr/bin/env python3
"""
Simple HTTP server for Hospital Analytics Dashboard
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler to serve files with proper MIME types"""
    
    def end_headers(self):
        # Add CORS headers for local development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def guess_type(self, path):
        """Guess the type of a file based on its URL"""
        mimetype, encoding = super().guess_type(path)
        
        # Ensure proper MIME types for our files
        if path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.css'):
            return 'text/css'
        elif path.endswith('.json'):
            return 'application/json'
        elif path.endswith('.html'):
            return 'text/html'
        
        return mimetype
    
    def log_message(self, format, *args):
        """Custom log message format"""
        print(f"🌐 {self.address_string()} - {format % args}")

def check_files():
    """Check if all required files exist"""
    required_files = [
        'index.html',
        'styles.css', 
        'dashboard.js',
        'dashboard_data.json'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ All required files found")
    return True

def start_server(port=8000):
    """Start the HTTP server"""
    
    # Check if files exist
    if not check_files():
        print("\n🔧 Please ensure all files are in the current directory:")
        print("   - index.html (main dashboard page)")
        print("   - styles.css (styling)")
        print("   - dashboard.js (functionality)")
        print("   - dashboard_data.json (data file)")
        print("\nRun 'python excel_to_json.py' first to generate the data file.")
        return
    
    # Try to find an available port
    original_port = port
    max_attempts = 10
    
    for attempt in range(max_attempts):
        try:
            with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
                print("🏥 Hospital Analytics Dashboard Server")
                print("=" * 50)
                print(f"🚀 Server starting on port {port}")
                print(f"🌐 Dashboard URL: http://localhost:{port}")
                print(f"📁 Serving files from: {os.getcwd()}")
                print("=" * 50)
                
                # Open browser automatically
                try:
                    webbrowser.open(f'http://localhost:{port}')
                    print("🔗 Opening dashboard in your default browser...")
                except:
                    print("💡 Please open http://localhost:{port} in your browser")
                
                print("\n📊 Dashboard Features:")
                print("   ✨ Interactive dark theme")
                print("   📈 Beautiful charts and graphs")
                print("   🔍 Data insights and analytics")
                print("   📱 Responsive design")
                print("   🎯 KPI tracking")
                
                print(f"\n🛑 Press Ctrl+C to stop the server")
                print("=" * 50)
                
                # Start serving
                httpd.serve_forever()
                
        except OSError as e:
            if e.errno == 98 or "Address already in use" in str(e):
                port += 1
                if attempt < max_attempts - 1:
                    print(f"⚠️  Port {port-1} is busy, trying port {port}...")
                    continue
                else:
                    print(f"❌ Could not find an available port after {max_attempts} attempts")
                    print(f"   Tried ports {original_port} to {port}")
                    return
            else:
                print(f"❌ Error starting server: {e}")
                return
        except KeyboardInterrupt:
            print("\n\n🛑 Server stopped by user")
            print("👋 Thank you for using Hospital Analytics Dashboard!")
            return

def main():
    """Main function"""
    print("🏥 Hospital Analytics Dashboard Server")
    print("Starting local development server...")
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Default port
    port = 8000
    
    # Check command line arguments for custom port
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
            if port < 1024 or port > 65535:
                print("⚠️  Port must be between 1024 and 65535")
                port = 8000
        except ValueError:
            print("⚠️  Invalid port number, using default port 8000")
    
    start_server(port)

if __name__ == "__main__":
    main()
