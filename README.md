# 🏥 Hospital Analytics Dashboard

A beautiful, interactive dark-themed dashboard for analyzing hospital data from Believers Church Medical College Hospital.

## ✨ Features

- **🌙 Dark Theme**: Beautiful dark interface optimized for extended viewing
- **📊 Interactive Charts**: Dynamic charts using Chart.js with hover effects
- **📱 Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **🔍 Data Insights**: AI-powered insights and recommendations
- **📈 Real-time Analytics**: KPI tracking and trend analysis
- **🎯 Multi-section Dashboard**: 
  - Overview with KPIs
  - Revenue Analysis
  - Performance Metrics
  - Pharmacy Analysis
  - Department Analysis
  - Insights & Recommendations

## 🚀 Quick Start

### 1. Generate Data
```bash
python excel_to_json.py
```
This converts your Excel file to JSON format for the web dashboard.

### 2. Start the Server
```bash
python server.py
```
Or use Python's built-in server:
```bash
python -m http.server 8000
```

### 3. Open Dashboard
Navigate to: `http://localhost:8000`

The dashboard will automatically open in your default browser.

## 📁 File Structure

```
📦 Hospital Dashboard
├── 📄 index.html              # Main dashboard HTML
├── 🎨 styles.css              # Dark theme CSS styling
├── ⚡ dashboard.js            # Interactive functionality
├── 🔧 server.py               # Local development server
├── 📊 excel_to_json.py        # Data conversion script
├── 📈 dashboard_data.json     # Processed data (auto-generated)
├── 📋 3.Monthly Dash Board - June '25.xlsx  # Source data
└── 📖 README.md               # This file
```

## 🎨 Dashboard Sections

### 📊 Overview
- **KPI Cards**: Total Revenue, Patients, Departments, Growth Rate
- **Revenue Trends**: Monthly comparison charts
- **Patient Volume**: OP vs IP distribution
- **Quick Insights**: AI-generated key findings

### 💰 Revenue Analysis
- **Revenue Breakdown**: Category-wise analysis
- **Month Comparison**: May vs June 2025
- **Detailed Tables**: Comprehensive revenue data
- **Growth Metrics**: Variance and percentage changes

### 📈 Performance Metrics
- **Radar Charts**: Multi-dimensional performance view
- **Trend Analysis**: Performance over time
- **Department Metrics**: Individual department performance

### 💊 Pharmacy Analysis
- **Margin Analysis**: Department-wise gross margins
- **Trend Charts**: 3-month margin trends
- **Top Performers**: Best performing departments

### 🏥 Department Analysis
- **OP/IP Tabs**: Switchable outpatient/inpatient views
- **Volume Charts**: Patient volume by department
- **Growth Analysis**: Department growth rates

### 💡 Insights & Recommendations
- **Key Findings**: Data-driven insights
- **Strategic Recommendations**: Actionable suggestions
- **Detailed Observations**: Comprehensive analysis

## 🎯 Key Metrics Tracked

- **Revenue**: Total, OP, IP, Growth rates
- **Patients**: Volume, Growth, Department-wise
- **Pharmacy**: Gross margins, Trends
- **Performance**: KPIs, Variance analysis
- **Departments**: Specialty-wise analysis

## 🛠️ Technical Details

### Technologies Used
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Charts**: Chart.js with dark theme
- **Icons**: Font Awesome
- **Fonts**: Inter (Google Fonts)
- **Backend**: Python HTTP server
- **Data**: JSON format

### Browser Compatibility
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### Performance Features
- **Lazy Loading**: Charts load on demand
- **Responsive Images**: Optimized for all screen sizes
- **Smooth Animations**: CSS transitions and transforms
- **Fast Loading**: Optimized assets and caching

## 🔧 Customization

### Colors
Edit the CSS variables in `styles.css`:
```css
:root {
    --accent-primary: #3b82f6;    /* Primary blue */
    --accent-secondary: #10b981;  /* Success green */
    --accent-warning: #f59e0b;    /* Warning orange */
    --accent-danger: #ef4444;     /* Danger red */
}
```

### Charts
Modify chart configurations in `dashboard.js`:
```javascript
const colors = {
    primary: '#3b82f6',
    secondary: '#10b981',
    // Add your custom colors
};
```

## 📊 Data Format

The dashboard expects data in this JSON structure:
```json
{
    "revenue_summary": [...],
    "financial_summary": [...],
    "performance_report": [...],
    "pharmacy_margins": [...],
    "specialty_op": [...],
    "specialty_ip": [...],
    "observations": [...],
    "kpis": {...}
}
```

## 🚨 Troubleshooting

### Port Already in Use
```bash
python server.py 8001  # Use different port
```

### Missing Data File
```bash
python excel_to_json.py  # Regenerate data
```

### Browser Cache Issues
- Hard refresh: `Ctrl+F5` (Windows) or `Cmd+Shift+R` (Mac)
- Clear browser cache for localhost

## 📱 Mobile Experience

The dashboard is fully responsive and provides an excellent mobile experience:
- **Touch-friendly**: Large buttons and touch targets
- **Swipe Navigation**: Smooth section transitions
- **Optimized Charts**: Mobile-optimized chart interactions
- **Readable Text**: Proper font scaling

## 🎉 Success!

Your Hospital Analytics Dashboard is now running! 

**Dashboard URL**: `http://localhost:8000`

Enjoy exploring your hospital data with beautiful visualizations and insights! 🏥✨
