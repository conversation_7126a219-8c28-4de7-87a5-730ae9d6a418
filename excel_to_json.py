import pandas as pd
import json
import numpy as np
from datetime import datetime

class ExcelToJsonConverter:
    """Convert Excel hospital data to JSON for web dashboard"""
    
    def __init__(self, excel_file):
        self.excel_file = excel_file
        self.data = {}
        
    def safe_convert_to_number(self, value):
        """Safely convert value to number"""
        if pd.isna(value):
            return 0
        try:
            return float(value)
        except:
            return 0
    
    def clean_string(self, value):
        """Clean string values"""
        if pd.isna(value):
            return ""
        return str(value).strip()
    
    def process_revenue_summary(self):
        """Process revenue summary sheet"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='Revenue Summary')
            
            # Find data starting row
            start_row = None
            for idx, row in df.iterrows():
                if 'PARTICULARS' in str(row.iloc[1]).upper():
                    start_row = idx + 1
                    break
            
            if start_row is None:
                return []
            
            revenue_data = []
            data_df = df.iloc[start_row:start_row + 20]  # Get first 20 rows of data
            
            for idx, row in data_df.iterrows():
                if pd.notna(row.iloc[1]):  # If there's a category
                    item = {
                        'category': self.clean_string(row.iloc[1]),
                        'subcategory': self.clean_string(row.iloc[2]),
                        'may_2025': self.safe_convert_to_number(row.iloc[4]),
                        'june_2025': self.safe_convert_to_number(row.iloc[5]),
                        'variance_amount': self.safe_convert_to_number(row.iloc[6]),
                        'variance_ratio': self.safe_convert_to_number(row.iloc[7])
                    }
                    if item['category'] and item['june_2025'] > 0:
                        revenue_data.append(item)
            
            return revenue_data
        except Exception as e:
            print(f"Error processing revenue summary: {e}")
            return []
    
    def process_financial_summary(self):
        """Process financial summary sheet"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='Financial Summary')
            
            start_row = None
            for idx, row in df.iterrows():
                if 'PARTICULARS' in str(row.iloc[1]).upper():
                    start_row = idx + 1
                    break
            
            if start_row is None:
                return []
            
            financial_data = []
            data_df = df.iloc[start_row:start_row + 15]
            
            for idx, row in data_df.iterrows():
                if pd.notna(row.iloc[1]):
                    item = {
                        'category': self.clean_string(row.iloc[1]),
                        'description': self.clean_string(row.iloc[2]),
                        'previous_month': self.safe_convert_to_number(row.iloc[4]),
                        'current_month': self.safe_convert_to_number(row.iloc[5]),
                        'variance_amount': self.safe_convert_to_number(row.iloc[6]),
                        'variance_ratio': self.safe_convert_to_number(row.iloc[7])
                    }
                    if item['category'] and item['current_month'] > 0:
                        financial_data.append(item)
            
            return financial_data
        except Exception as e:
            print(f"Error processing financial summary: {e}")
            return []
    
    def process_performance_report(self):
        """Process performance report sheet"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='Performance Report')
            
            start_row = None
            for idx, row in df.iterrows():
                if 'PARTICULARS' in str(row.iloc[1]).upper():
                    start_row = idx + 1
                    break
            
            if start_row is None:
                return []
            
            performance_data = []
            data_df = df.iloc[start_row:start_row + 20]
            
            for idx, row in data_df.iterrows():
                if pd.notna(row.iloc[1]):
                    item = {
                        'category': self.clean_string(row.iloc[1]),
                        'metric': self.clean_string(row.iloc[2]),
                        'previous_month': self.safe_convert_to_number(row.iloc[4]),
                        'current_month': self.safe_convert_to_number(row.iloc[5]),
                        'variance_amount': self.safe_convert_to_number(row.iloc[6]),
                        'variance_ratio': self.safe_convert_to_number(row.iloc[7])
                    }
                    if item['metric'] and item['current_month'] > 0:
                        performance_data.append(item)
            
            return performance_data
        except Exception as e:
            print(f"Error processing performance report: {e}")
            return []
    
    def process_pharmacy_margins(self):
        """Process pharmacy margins sheet"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='Pharmacy - Gross Margin Ratio')
            
            start_row = None
            for idx, row in df.iterrows():
                if 'ADVISING SPECALISATION' in str(row.iloc[1]).upper():
                    start_row = idx + 1
                    break
            
            if start_row is None:
                return []
            
            pharmacy_data = []
            data_df = df.iloc[start_row:start_row + 30]
            
            for idx, row in data_df.iterrows():
                if pd.notna(row.iloc[1]):
                    item = {
                        'department': self.clean_string(row.iloc[1]),
                        'apr_2025': self.safe_convert_to_number(row.iloc[2]),
                        'may_2025': self.safe_convert_to_number(row.iloc[3]),
                        'june_2025': self.safe_convert_to_number(row.iloc[4])
                    }
                    if item['department'] and item['june_2025'] > 0:
                        pharmacy_data.append(item)
            
            return pharmacy_data
        except Exception as e:
            print(f"Error processing pharmacy margins: {e}")
            return []
    
    def process_specialty_op(self):
        """Process specialty OP sheet"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='Specalitywise OP')
            
            start_row = None
            for idx, row in df.iterrows():
                if 'Sl No' in str(row.iloc[0]) or 'depT' in str(row.iloc[1]):
                    start_row = idx + 1
                    break
            
            if start_row is None:
                return []
            
            op_data = []
            data_df = df.iloc[start_row:start_row + 50]
            
            for idx, row in data_df.iterrows():
                if pd.notna(row.iloc[1]) and pd.notna(row.iloc[5]):
                    item = {
                        'department': self.clean_string(row.iloc[1]),
                        'unit': self.clean_string(row.iloc[2]),
                        'previous_month': self.safe_convert_to_number(row.iloc[4]),
                        'current_month': self.safe_convert_to_number(row.iloc[5]),
                        'variance_amount': self.safe_convert_to_number(row.iloc[6]),
                        'variance_ratio': self.safe_convert_to_number(row.iloc[7])
                    }
                    if item['department'] and item['current_month'] > 0:
                        op_data.append(item)
            
            return op_data
        except Exception as e:
            print(f"Error processing specialty OP: {e}")
            return []
    
    def process_specialty_ip(self):
        """Process specialty IP sheet"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='Specalitywise IP')
            
            start_row = None
            for idx, row in df.iterrows():
                if 'Sl No' in str(row.iloc[0]):
                    start_row = idx + 1
                    break
            
            if start_row is None:
                return []
            
            ip_data = []
            data_df = df.iloc[start_row:start_row + 50]
            
            for idx, row in data_df.iterrows():
                if pd.notna(row.iloc[1]) and pd.notna(row.iloc[5]):
                    item = {
                        'department': self.clean_string(row.iloc[1]),
                        'unit': self.clean_string(row.iloc[2]),
                        'previous_month': self.safe_convert_to_number(row.iloc[4]),
                        'current_month': self.safe_convert_to_number(row.iloc[5]),
                        'variance_amount': self.safe_convert_to_number(row.iloc[6]),
                        'variance_ratio': self.safe_convert_to_number(row.iloc[7])
                    }
                    if item['department'] and item['current_month'] > 0:
                        ip_data.append(item)
            
            return ip_data
        except Exception as e:
            print(f"Error processing specialty IP: {e}")
            return []
    
    def process_observations(self):
        """Process observations sheet"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='Observations & Recommendations')
            
            observations = []
            for idx, row in df.iterrows():
                if pd.notna(row.iloc[1]) and len(str(row.iloc[1])) > 10:
                    observations.append(self.clean_string(row.iloc[1]))
            
            return observations
        except Exception as e:
            print(f"Error processing observations: {e}")
            return []
    
    def calculate_kpis(self):
        """Calculate key performance indicators"""
        revenue_data = self.data.get('revenue_summary', [])
        performance_data = self.data.get('performance_report', [])
        op_data = self.data.get('specialty_op', [])
        ip_data = self.data.get('specialty_ip', [])
        
        kpis = {
            'total_revenue': sum(item['june_2025'] for item in revenue_data),
            'revenue_growth': sum(item['variance_ratio'] for item in revenue_data) / len(revenue_data) if revenue_data else 0,
            'total_op_patients': sum(item['current_month'] for item in op_data),
            'total_ip_patients': sum(item['current_month'] for item in ip_data),
            'op_growth': sum(item['variance_ratio'] for item in op_data) / len(op_data) if op_data else 0,
            'ip_growth': sum(item['variance_ratio'] for item in ip_data) / len(ip_data) if ip_data else 0
        }
        
        return kpis
    
    def convert_to_json(self):
        """Convert all data to JSON format"""
        print("Processing Excel data...")
        
        self.data = {
            'revenue_summary': self.process_revenue_summary(),
            'financial_summary': self.process_financial_summary(),
            'performance_report': self.process_performance_report(),
            'pharmacy_margins': self.process_pharmacy_margins(),
            'specialty_op': self.process_specialty_op(),
            'specialty_ip': self.process_specialty_ip(),
            'observations': self.process_observations(),
            'last_updated': datetime.now().isoformat()
        }
        
        # Calculate KPIs
        self.data['kpis'] = self.calculate_kpis()
        
        # Save to JSON file
        with open('dashboard_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.data, f, indent=2, ensure_ascii=False)
        
        print("✅ Data converted successfully!")
        print(f"📊 Revenue items: {len(self.data['revenue_summary'])}")
        print(f"💰 Financial items: {len(self.data['financial_summary'])}")
        print(f"📈 Performance items: {len(self.data['performance_report'])}")
        print(f"💊 Pharmacy departments: {len(self.data['pharmacy_margins'])}")
        print(f"🏥 OP departments: {len(self.data['specialty_op'])}")
        print(f"🛏️ IP departments: {len(self.data['specialty_ip'])}")
        print(f"💡 Observations: {len(self.data['observations'])}")
        
        return self.data

if __name__ == "__main__":
    converter = ExcelToJsonConverter("3.Monthly Dash Board - June '25.xlsx")
    data = converter.convert_to_json()
    print("\n🎉 JSON data file created: dashboard_data.json")
