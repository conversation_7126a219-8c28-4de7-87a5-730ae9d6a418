#!/usr/bin/env python3
"""
Test script for the hospital dashboard
"""

import sys
import traceback

def test_imports():
    """Test all imports"""
    print("Testing imports...")
    
    try:
        import pandas as pd
        print("✅ pandas imported successfully")
    except Exception as e:
        print(f"❌ pandas import failed: {e}")
        return False
    
    try:
        import plotly.express as px
        import plotly.graph_objects as go
        print("✅ plotly imported successfully")
    except Exception as e:
        print(f"❌ plotly import failed: {e}")
        return False
    
    try:
        from data_processor import HospitalDataProcessor
        print("✅ data_processor imported successfully")
    except Exception as e:
        print(f"❌ data_processor import failed: {e}")
        return False
    
    try:
        from analytics import HospitalAnalytics
        print("✅ analytics imported successfully")
    except Exception as e:
        print(f"❌ analytics import failed: {e}")
        return False
    
    try:
        from visualizations import apply_dark_theme
        print("✅ visualizations imported successfully")
    except Exception as e:
        print(f"❌ visualizations import failed: {e}")
        return False
    
    return True

def test_data_processing():
    """Test data processing"""
    print("\nTesting data processing...")
    
    try:
        from data_processor import HospitalDataProcessor
        
        # Initialize processor
        processor = HospitalDataProcessor("3.Monthly Dash Board - June '25.xlsx")
        print("✅ HospitalDataProcessor initialized")
        
        # Process data
        data = processor.process_all_data()
        print("✅ Data processed successfully")
        
        # Get metrics
        metrics = processor.get_key_metrics()
        print("✅ Metrics calculated successfully")
        
        print(f"📊 Processed {len(data)} data sheets")
        print(f"📈 Calculated {len(metrics)} key metrics")
        
        return True, data, metrics
        
    except Exception as e:
        print(f"❌ Data processing failed: {e}")
        traceback.print_exc()
        return False, None, None

def test_analytics():
    """Test analytics"""
    print("\nTesting analytics...")
    
    try:
        from data_processor import HospitalDataProcessor
        from analytics import HospitalAnalytics
        
        # Get data
        processor = HospitalDataProcessor("3.Monthly Dash Board - June '25.xlsx")
        data = processor.process_all_data()
        
        # Initialize analytics
        analytics = HospitalAnalytics(data)
        print("✅ HospitalAnalytics initialized")
        
        # Get executive summary
        summary = analytics.get_executive_summary()
        print("✅ Executive summary generated")
        
        print(f"💡 Generated {len(summary['insights'])} insights")
        print(f"🎯 Generated {len(summary['recommendations'])} recommendations")
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics failed: {e}")
        traceback.print_exc()
        return False

def test_visualizations():
    """Test visualizations"""
    print("\nTesting visualizations...")
    
    try:
        from data_processor import HospitalDataProcessor
        from visualizations import create_revenue_waterfall, apply_dark_theme
        import plotly.graph_objects as go
        
        # Get data
        processor = HospitalDataProcessor("3.Monthly Dash Board - June '25.xlsx")
        data = processor.process_all_data()
        
        # Test creating a chart
        fig = create_revenue_waterfall(data['revenue_summary'])
        print("✅ Revenue waterfall chart created")
        
        # Test applying dark theme
        fig = apply_dark_theme(fig)
        print("✅ Dark theme applied")
        
        return True
        
    except Exception as e:
        print(f"❌ Visualizations failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🏥 Hospital Dashboard Test Suite")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed. Exiting.")
        sys.exit(1)
    
    # Test data processing
    success, data, metrics = test_data_processing()
    if not success:
        print("\n❌ Data processing tests failed. Exiting.")
        sys.exit(1)
    
    # Test analytics
    if not test_analytics():
        print("\n❌ Analytics tests failed. Exiting.")
        sys.exit(1)
    
    # Test visualizations
    if not test_visualizations():
        print("\n❌ Visualization tests failed. Exiting.")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed successfully!")
    print("✅ Dashboard is ready to run")
    print("\nTo start the dashboard, run:")
    print("streamlit run dashboard.py")

if __name__ == "__main__":
    main()
