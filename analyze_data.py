import pandas as pd
import numpy as np
from pathlib import Path

def analyze_excel_file(file_path):
    """Analyze the structure and content of the Excel file"""
    print(f"Analyzing file: {file_path}")
    print("=" * 50)
    
    try:
        # Read Excel file and get sheet names
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"Number of sheets: {len(sheet_names)}")
        print(f"Sheet names: {sheet_names}")
        print("\n")
        
        # Analyze each sheet
        for sheet_name in sheet_names:
            print(f"SHEET: {sheet_name}")
            print("-" * 30)
            
            # Read the sheet
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            print(f"Data types:")
            for col, dtype in df.dtypes.items():
                print(f"  {col}: {dtype}")
            
            print(f"\nFirst few rows:")
            print(df.head())
            
            print(f"\nBasic statistics:")
            print(df.describe(include='all'))
            
            print(f"\nMissing values:")
            missing = df.isnull().sum()
            if missing.sum() > 0:
                print(missing[missing > 0])
            else:
                print("No missing values")
            
            print("\n" + "="*50 + "\n")
    
    except Exception as e:
        print(f"Error analyzing file: {e}")

if __name__ == "__main__":
    file_path = "3.Monthly Dash Board - June '25.xlsx"
    analyze_excel_file(file_path)
