/* Hospital Analytics Dashboard - Dark Theme */

:root {
    /* Dark Theme Colors */
    --bg-primary: #0a0e1a;
    --bg-secondary: #1a1f2e;
    --bg-tertiary: #252b3d;
    --bg-card: #1e2330;
    --bg-hover: #2a3142;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --text-muted: #8892a6;
    
    /* Accent Colors */
    --accent-primary: #3b82f6;
    --accent-secondary: #10b981;
    --accent-warning: #f59e0b;
    --accent-danger: #ef4444;
    --accent-purple: #8b5cf6;
    --accent-cyan: #06b6d4;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #3b82f6, #1d4ed8);
    --gradient-secondary: linear-gradient(135deg, #10b981, #059669);
    --gradient-warning: linear-gradient(135deg, #f59e0b, #d97706);
    --gradient-danger: linear-gradient(135deg, #ef4444, #dc2626);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.loading-content {
    text-align: center;
    animation: fadeInUp 0.8s ease-out;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--bg-tertiary);
    border-top: 4px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

.loading-content h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-content p {
    color: var(--text-secondary);
}

/* Dashboard Layout */
.dashboard {
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar main";
    grid-template-columns: 280px 1fr;
    grid-template-rows: 80px 1fr;
    height: 100vh;
    transition: opacity var(--transition-slow);
}

.dashboard.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Header */
.header {
    grid-area: header;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--bg-tertiary);
    display: flex;
    align-items: center;
    padding: 0 2rem;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section i {
    font-size: 2rem;
    color: var(--accent-primary);
}

.logo-text h1 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.logo-text p {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.date-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.refresh-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition-fast);
}

.refresh-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.refresh-btn i {
    transition: transform var(--transition-fast);
}

.refresh-btn:hover i {
    transform: rotate(180deg);
}

/* Sidebar */
.sidebar {
    grid-area: sidebar;
    background: var(--bg-secondary);
    border-right: 1px solid var(--bg-tertiary);
    padding: 2rem 0;
    overflow-y: auto;
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0 1rem;
}

.nav-item {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all var(--transition-fast);
    text-align: left;
    width: 100%;
}

.nav-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    transform: translateX(4px);
}

.nav-item.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-item i {
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    grid-area: main;
    background: var(--bg-primary);
    padding: 2rem;
    overflow-y: auto;
    height: calc(100vh - 80px);
}

.content-section {
    display: none;
    animation: fadeInUp 0.5s ease-out;
}

.content-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-header h2 i {
    color: var(--accent-primary);
}

.section-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* KPI Grid */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.kpi-card {
    background: var(--bg-card);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.kpi-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-primary);
}

.kpi-card:hover::before {
    transform: scaleX(1);
}

.kpi-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.kpi-icon.revenue {
    background: var(--gradient-primary);
}

.kpi-icon.patients {
    background: var(--gradient-secondary);
}

.kpi-icon.departments {
    background: var(--gradient-warning);
}

.kpi-icon.growth {
    background: var(--gradient-danger);
}

.kpi-content h3 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.kpi-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.kpi-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
}

.kpi-change.positive {
    background: rgba(16, 185, 129, 0.2);
    color: var(--accent-secondary);
}

.kpi-change.negative {
    background: rgba(239, 68, 68, 0.2);
    color: var(--accent-danger);
}

.kpi-change.neutral {
    background: rgba(139, 92, 246, 0.2);
    color: var(--accent-purple);
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: var(--bg-card);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    transition: all var(--transition-normal);
}

.chart-container.large {
    grid-column: 1 / -1;
}

.chart-container:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--accent-primary);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--bg-tertiary);
}

.chart-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.chart-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.chart-btn.active {
    background: var(--accent-primary);
    color: white;
}

.chart-select {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--bg-hover);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
}

.chart-legend {
    display: flex;
    gap: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.legend-item::before {
    content: '';
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-item.op::before {
    background: var(--accent-primary);
}

.legend-item.ip::before {
    background: var(--accent-secondary);
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Insights and Tables */
.insights-preview {
    background: var(--bg-card);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.insights-preview h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.insight-item {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--accent-primary);
    transition: all var(--transition-fast);
}

.insight-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.insight-item.positive {
    border-left-color: var(--accent-secondary);
}

.insight-item.warning {
    border-left-color: var(--accent-warning);
}

.insight-item.danger {
    border-left-color: var(--accent-danger);
}

/* Data Tables */
.data-table-container {
    background: var(--bg-card);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.data-table-container h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.data-table th,
.data-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--bg-tertiary);
}

.data-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
}

.data-table td {
    color: var(--text-secondary);
}

.data-table tr:hover {
    background: var(--bg-hover);
}

/* Department Tabs */
.department-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.tab-btn {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.tab-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

/* Insights Section */
.insights-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.insights-column {
    background: var(--bg-card);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
}

.insights-column h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.insights-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.insight-card {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--accent-primary);
    transition: all var(--transition-fast);
}

.insight-card:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.insight-card.finding {
    border-left-color: var(--accent-secondary);
}

.insight-card.recommendation {
    border-left-color: var(--accent-warning);
}

.observations-section {
    background: var(--bg-card);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
}

.observations-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.observations-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.observation-item {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    line-height: 1.6;
    transition: all var(--transition-fast);
}

.observation-item:hover {
    background: var(--bg-hover);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-success {
    color: var(--accent-secondary);
}

.text-warning {
    color: var(--accent-warning);
}

.text-danger {
    color: var(--accent-danger);
}

.bg-success {
    background: rgba(16, 185, 129, 0.2);
}

.bg-warning {
    background: rgba(245, 158, 11, 0.2);
}

.bg-danger {
    background: rgba(239, 68, 68, 0.2);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard {
        grid-template-columns: 240px 1fr;
    }

    .kpi-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .insights-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard {
        grid-template-areas:
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 80px 1fr;
    }

    .sidebar {
        position: fixed;
        top: 80px;
        left: -280px;
        width: 280px;
        height: calc(100vh - 80px);
        z-index: 1000;
        transition: left var(--transition-normal);
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        padding: 1rem;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .insights-grid {
        grid-template-columns: 1fr;
    }

    .department-tabs {
        flex-wrap: wrap;
    }
}
